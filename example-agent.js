// example-agent.js - Example of how to create different types of agents
const { io } = require("socket.io-client");

// Configuration for different agent types
const agentConfigs = {
    callLogger: {
        agentId: "call-logger-001",
        agentName: "Call Logger Agent",
        agentType: "call_processor"
    },
    notificationAgent: {
        agentId: "notification-001", 
        agentName: "Notification Agent",
        agentType: "notification_service"
    },
    analyticsAgent: {
        agentId: "analytics-001",
        agentName: "Analytics Agent", 
        agentType: "analytics_processor"
    }
};

// Choose which agent to run (you can change this)
const selectedAgent = process.argv[2] || 'callLogger';
const agentConfig = agentConfigs[selectedAgent];

if (!agentConfig) {
    console.error(`❌ Unknown agent type: ${selectedAgent}`);
    console.log('Available agents:', Object.keys(agentConfigs).join(', '));
    process.exit(1);
}

console.log(`🚀 Starting ${agentConfig.agentName}...`);

const socket = io("http://localhost:3000"); // Change to your server URL

socket.on("connect", () => {
    console.log("✅ Connected to socket server");
    
    // Register this client as an agent
    socket.emit('registerAgent', agentConfig);
});

// Handle registration responses
socket.on("registrationSuccess", (data) => {
    console.log("🎉 Agent registration successful:", data);
    console.log("📋 Connected agents:", data.connectedAgents);
});

socket.on("registrationError", (data) => {
    console.error("❌ Agent registration failed:", data.message);
});

// Handle agent connection/disconnection events
socket.on("agentConnected", (data) => {
    console.log("🔗 New agent connected:", data);
});

socket.on("agentDisconnected", (data) => {
    console.log("🔌 Agent disconnected:", data.agentId);
});

// Handle call events based on agent type
socket.on("incomingCall", (data) => {
    console.log(`📞 [${agentConfig.agentName}] Incoming call received:`, data);
    
    // Different agents can handle the same event differently
    switch (agentConfig.agentType) {
        case 'call_processor':
            console.log("💾 Processing call for database storage...");
            break;
        case 'notification_service':
            console.log("🔔 Sending notification for incoming call...");
            break;
        case 'analytics_processor':
            console.log("📊 Analyzing incoming call patterns...");
            break;
    }
});

socket.on("outgoingCall", (data) => {
    console.log(`📞 [${agentConfig.agentName}] Outgoing call received:`, data);
    
    switch (agentConfig.agentType) {
        case 'call_processor':
            console.log("💾 Processing outgoing call for database storage...");
            break;
        case 'notification_service':
            console.log("🔔 Sending notification for outgoing call...");
            break;
        case 'analytics_processor':
            console.log("📊 Analyzing outgoing call patterns...");
            break;
    }
});

socket.on("callRecording", (data) => {
    console.log(`📞 [${agentConfig.agentName}] Call recording received:`, data);
    
    switch (agentConfig.agentType) {
        case 'call_processor':
            console.log("💾 Processing call recording for storage...");
            break;
        case 'notification_service':
            console.log("🔔 Sending notification for call recording...");
            break;
        case 'analytics_processor':
            console.log("📊 Analyzing call recording data...");
            break;
    }
});

socket.on("disconnect", () => {
    console.log("🔌 Disconnected from socket server");
});

// Handle process termination gracefully
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down agent...');
    socket.disconnect();
    process.exit(0);
});

console.log(`📡 Agent ${agentConfig.agentId} is ready to receive events`);
console.log('Press Ctrl+C to stop the agent');
