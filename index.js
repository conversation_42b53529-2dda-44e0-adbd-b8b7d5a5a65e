// index.js or server.js
const express = require('express');
const bodyParser = require('body-parser');
const http = require('http');
const { Server } = require('socket.io');

const app = express();
const server = http.createServer(app);

// Initialize Socket.IO with CORS enabled for Android apps (or any client)
const io = new Server(server, {
  cors: {
    origin: "*",     // For testing allow all origins; update to your app URL/domain in production
    methods: ["GET", "POST"]
  }
});

const port = 3000;

// Middleware to parse JSON bodies
app.use(bodyParser.json());

// Store connected agents with their identities (Map: agentId -> socket, Map: agentNumber -> agentId)
const connectedAgents = new Map();
const agentNumberToId = new Map();

io.on('connection', (socket) => {
    console.log(`🔗 WebSocket client connected: ${socket.id}`);

    // Wait for agent registration
    socket.on('registerAgent', (agentData) => {
        const { agentId, agentNumber, agentName, agentType } = agentData;

        if (!agentId) {
            socket.emit('registrationError', { message: 'Agent ID is required' });
            return;
        }

        if (!agentNumber) {
            socket.emit('registrationError', { message: 'Agent Number (phone number) is required' });
            return;
        }

        // Check if agent number is already registered
        if (agentNumberToId.has(agentNumber)) {
            socket.emit('registrationError', { message: `Agent number ${agentNumber} is already registered` });
            return;
        }

        // Store agent information
        const agentInfo = {
            socket: socket,
            agentId: agentId,
            agentNumber: agentNumber,
            agentName: agentName || `Agent-${agentNumber}`,
            agentType: agentType || 'default',
            connectedAt: new Date(),
            socketId: socket.id
        };

        connectedAgents.set(agentId, agentInfo);
        agentNumberToId.set(agentNumber, agentId);
        socket.agentId = agentId;
        socket.agentNumber = agentNumber;

        console.log(`✅ Agent registered: ${agentId} (${agentNumber}) - ${agentInfo.agentName}`);
        socket.emit('registrationSuccess', {
            message: 'Agent registered successfully',
            agentId: agentId,
            agentNumber: agentNumber,
            connectedAgents: Array.from(connectedAgents.keys()),
            connectedNumbers: Array.from(agentNumberToId.keys())
        });

        // Notify other agents about new connection
        socket.broadcast.emit('agentConnected', {
            agentId: agentId,
            agentNumber: agentNumber,
            agentName: agentInfo.agentName,
            agentType: agentInfo.agentType
        });
    });

    socket.on('disconnect', () => {
        if (socket.agentId && socket.agentNumber) {
            console.log(`❌ Agent disconnected: ${socket.agentId} (${socket.agentNumber})`);
            connectedAgents.delete(socket.agentId);
            agentNumberToId.delete(socket.agentNumber);

            // Notify other agents about disconnection
            socket.broadcast.emit('agentDisconnected', {
                agentId: socket.agentId,
                agentNumber: socket.agentNumber
            });
        } else {
            console.log(`❌ Unregistered client disconnected: ${socket.id}`);
        }
    });
});

// POST route for call events - broadcast to all agents
app.post('/api/:type', (req, res) => {
    const { type } = req.params;
    const validTypes = ['incomingCall', 'outgoingCall', 'callRecording'];

    if (!validTypes.includes(type)) {
        return res.status(400).json({ status: 'error', message: 'Invalid event type' });
    }

    const data = req.body;

    if (connectedAgents.size > 0) {
        // Emit event to all connected agents
        connectedAgents.forEach((agentInfo, agentId) => {
            agentInfo.socket.emit(type, data);
        });
        return res.json({
            status: 'success',
            message: `${type} event sent to ${connectedAgents.size} agent(s)`,
            connectedAgents: Array.from(connectedAgents.keys())
        });
    } else {
        return res.status(503).json({ status: 'error', message: 'No agents connected' });
    }
});

// POST route for sending events to specific agent(s)
app.post('/api/:type/agent/:agentId', (req, res) => {
    const { type, agentId } = req.params;
    const validTypes = ['incomingCall', 'outgoingCall', 'callRecording'];

    if (!validTypes.includes(type)) {
        return res.status(400).json({ status: 'error', message: 'Invalid event type' });
    }

    const data = req.body;
    const agentInfo = connectedAgents.get(agentId);

    if (!agentInfo) {
        return res.status(404).json({
            status: 'error',
            message: `Agent ${agentId} not found or not connected`,
            connectedAgents: Array.from(connectedAgents.keys())
        });
    }

    agentInfo.socket.emit(type, data);
    return res.json({
        status: 'success',
        message: `${type} event sent to agent ${agentId}`,
        targetAgent: agentId
    });
});

// POST route for sending events to multiple specific agents
app.post('/api/:type/agents', (req, res) => {
    const { type } = req.params;
    const { agentIds, ...data } = req.body;
    const validTypes = ['incomingCall', 'outgoingCall', 'callRecording'];

    if (!validTypes.includes(type)) {
        return res.status(400).json({ status: 'error', message: 'Invalid event type' });
    }

    if (!agentIds || !Array.isArray(agentIds)) {
        return res.status(400).json({ status: 'error', message: 'agentIds array is required in request body' });
    }

    const sentTo = [];
    const notFound = [];

    agentIds.forEach(agentId => {
        const agentInfo = connectedAgents.get(agentId);
        if (agentInfo) {
            agentInfo.socket.emit(type, data);
            sentTo.push(agentId);
        } else {
            notFound.push(agentId);
        }
    });

    return res.json({
        status: sentTo.length > 0 ? 'success' : 'error',
        message: `${type} event sent to ${sentTo.length} agent(s)`,
        sentTo: sentTo,
        notFound: notFound.length > 0 ? notFound : undefined,
        connectedAgents: Array.from(connectedAgents.keys())
    });
});

// GET route to list all connected agents
app.get('/api/agents', (req, res) => {
    const agents = Array.from(connectedAgents.entries()).map(([agentId, agentInfo]) => ({
        agentId: agentId,
        agentName: agentInfo.agentName,
        agentType: agentInfo.agentType,
        connectedAt: agentInfo.connectedAt,
        socketId: agentInfo.socketId
    }));

    return res.json({
        status: 'success',
        totalAgents: agents.length,
        agents: agents
    });
});

// GET route to check if specific agent is connected
app.get('/api/agents/:agentId', (req, res) => {
    const { agentId } = req.params;
    const agentInfo = connectedAgents.get(agentId);

    if (!agentInfo) {
        return res.status(404).json({
            status: 'error',
            message: `Agent ${agentId} not found or not connected`
        });
    }

    return res.json({
        status: 'success',
        agent: {
            agentId: agentId,
            agentName: agentInfo.agentName,
            agentType: agentInfo.agentType,
            connectedAt: agentInfo.connectedAt,
            socketId: agentInfo.socketId
        }
    });
});

// Start server
server.listen(port, () => {
    console.log(`🚀 Server running at http://localhost:${port}`);
    console.log(`📡 Socket.IO server ready for agent connections`);
    console.log(`🔗 Available endpoints:`);
    console.log(`   GET  /api/agents - List all connected agents`);
    console.log(`   GET  /api/agents/:agentId - Check specific agent status`);
    console.log(`   POST /api/:type - Broadcast to all agents`);
    console.log(`   POST /api/:type/agent/:agentId - Send to specific agent`);
    console.log(`   POST /api/:type/agents - Send to multiple agents`);
});
